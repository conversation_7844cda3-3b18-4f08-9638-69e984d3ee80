package com.br.sasw.satwebservice.controller;

import com.br.sasw.satwebservice.service.EsocialService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import com.br.sasw.satwebservice.service.EsocialService.FullResponse;

@RestController
@RequiredArgsConstructor
@Tag(name = "Envio e Consulta ESOCIAL")
@RequestMapping("/esocial")
public class ESocialSenderController {

    private final EsocialService esocialService;

    @PostMapping(path = "/envio")
    private ResponseEntity<FullResponse> send(@RequestBody String xml,
                                        String id,
                                        @RequestParam String empresa,
                                        @RequestParam String nrInscEmpregador,
                                        @RequestParam String tpInscEmpregador,
										@RequestParam String nrInscTransmissor,
                                        @RequestParam String tpInscTransmissor,
										@RequestParam Integer idGrupo){


        return ResponseEntity.ok().body(esocialService.send(xml, id, empresa, nrInscEmpregador, tpInscEmpregador, nrInscTransmissor, tpInscTransmissor, idGrupo));
    }

    @PostMapping(path = "/consulta")
    public ResponseEntity<String> get(@RequestParam String protocolo, @RequestParam String empresa) {

        return ResponseEntity.ok().body(esocialService.get(protocolo, empresa));
    }
}
