package com.br.sasw.satwebservice.controller;

import com.br.sasw.satwebservice.exception.BusinessException;
import com.br.sasw.satwebservice.exception.CertificateException;
import com.br.sasw.satwebservice.exception.XmlSigningException;
import io.swagger.v3.oas.annotations.Hidden;
import org.springframework.web.bind.annotation.*;

/**
 * Controller para testar o exception handler
 * Este controller será removido em produção
 */
@RestController
@RequestMapping("/test-exceptions")
@Hidden // Oculta do Swagger
public class TestExceptionController {

    @PostMapping("/missing-body")
    public String testMissingBody(@RequestBody String body) {
        return "OK";
    }

    @GetMapping("/missing-param")
    public String testMissingParam(@RequestParam String requiredParam) {
        return "OK";
    }

    @GetMapping("/wrong-param-type")
    public String testWrongParamType(@RequestParam Integer number) {
        return "OK";
    }

    @GetMapping("/business-error")
    public String testBusinessError() {
        throw new BusinessException("Este é um erro de regra de negócio de teste");
    }

    @GetMapping("/certificate-error")
    public String testCertificateError() {
        throw new CertificateException("Erro de certificado digital de teste");
    }

    @GetMapping("/xml-signing-error")
    public String testXmlSigningError() {
        throw new XmlSigningException("Erro de assinatura XML de teste");
    }

    @GetMapping("/runtime-error")
    public String testRuntimeError() {
        throw new RuntimeException("Erro de runtime de teste");
    }

    @GetMapping("/generic-error")
    public String testGenericError() throws Exception {
        throw new Exception("Erro genérico de teste");
    }
}
