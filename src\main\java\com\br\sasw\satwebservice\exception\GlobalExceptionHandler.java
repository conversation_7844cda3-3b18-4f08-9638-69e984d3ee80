package com.br.sasw.satwebservice.exception;

import com.br.sasw.satwebservice.dto.ErrorResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import java.time.LocalDateTime;

/**
 * Handler global para tratamento de exceções da API
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * Trata erros de corpo da requisição ausente ou malformado
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public ResponseEntity<ErrorResponse> handleHttpMessageNotReadable(
            HttpMessageNotReadableException ex, WebRequest request) {

        log.warn("Erro de leitura da mensagem HTTP: {}", ex.getMessage());

        String message = "Corpo da requisição é obrigatório e deve estar em formato válido";
        String details = null;

        // Detecta tipos específicos de erro
        if (ex.getMessage().contains("Required request body is missing")) {
            message = "Corpo da requisição é obrigatório";
            details = "Verifique se você está enviando o corpo da requisição (JSON/XML) corretamente";
        } else if (ex.getMessage().contains("JSON parse error")) {
            message = "Formato JSON inválido";
            details = "Verifique a sintaxe do JSON enviado";
        } else if (ex.getMessage().contains("XML")) {
            message = "Formato XML inválido";
            details = "Verifique a sintaxe do XML enviado";
        }

        ErrorResponse errorResponse = ErrorResponse.builder()
                .timestamp(LocalDateTime.now())
                .status(HttpStatus.BAD_REQUEST.value())
                .error(HttpStatus.BAD_REQUEST.getReasonPhrase())
                .message(message)
                .details(details)
                .path(getPath(request))
                .build();

        return ResponseEntity.badRequest().body(errorResponse);
    }

    /**
     * Trata erros de parâmetros obrigatórios ausentes
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    public ResponseEntity<ErrorResponse> handleMissingServletRequestParameter(
            MissingServletRequestParameterException ex, WebRequest request) {

        log.warn("Parâmetro obrigatório ausente: {}", ex.getParameterName());

        String message = String.format("Parâmetro obrigatório '%s' está ausente", ex.getParameterName());
        String details = String.format("O parâmetro '%s' do tipo '%s' é obrigatório para esta requisição",
                ex.getParameterName(), ex.getParameterType());

        ErrorResponse errorResponse = ErrorResponse.builder()
                .timestamp(LocalDateTime.now())
                .status(HttpStatus.BAD_REQUEST.value())
                .error(HttpStatus.BAD_REQUEST.getReasonPhrase())
                .message(message)
                .details(details)
                .path(getPath(request))
                .build();

        return ResponseEntity.badRequest().body(errorResponse);
    }

    /**
     * Trata erros de tipo de parâmetro incorreto
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public ResponseEntity<ErrorResponse> handleMethodArgumentTypeMismatch(
            MethodArgumentTypeMismatchException ex, WebRequest request) {

        log.warn("Tipo de parâmetro incorreto: {} = {}", ex.getName(), ex.getValue());

        String message = String.format("Valor inválido para o parâmetro '%s'", ex.getName());
        String details = String.format("O valor '%s' não é válido para o parâmetro '%s'. Tipo esperado: %s",
                ex.getValue(), ex.getName(), ex.getRequiredType().getSimpleName());

        ErrorResponse errorResponse = ErrorResponse.builder()
                .timestamp(LocalDateTime.now())
                .status(HttpStatus.BAD_REQUEST.value())
                .error(HttpStatus.BAD_REQUEST.getReasonPhrase())
                .message(message)
                .details(details)
                .path(getPath(request))
                .build();

        return ResponseEntity.badRequest().body(errorResponse);
    }

    /**
     * Trata erros de método HTTP não suportado
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public ResponseEntity<ErrorResponse> handleHttpRequestMethodNotSupported(
            HttpRequestMethodNotSupportedException ex, WebRequest request) {

        log.warn("Método HTTP não suportado: {}", ex.getMethod());

        String message = String.format("Método HTTP '%s' não é suportado para este endpoint", ex.getMethod());
        String details = String.format("Métodos suportados: %s", String.join(", ", ex.getSupportedMethods()));

        ErrorResponse errorResponse = ErrorResponse.builder()
                .timestamp(LocalDateTime.now())
                .status(HttpStatus.METHOD_NOT_ALLOWED.value())
                .error(HttpStatus.METHOD_NOT_ALLOWED.getReasonPhrase())
                .message(message)
                .details(details)
                .path(getPath(request))
                .build();

        return ResponseEntity.status(HttpStatus.METHOD_NOT_ALLOWED).body(errorResponse);
    }

    /**
     * Trata erros de tipo de mídia não suportado
     */
    @ExceptionHandler(HttpMediaTypeNotSupportedException.class)
    public ResponseEntity<ErrorResponse> handleHttpMediaTypeNotSupported(
            HttpMediaTypeNotSupportedException ex, WebRequest request) {

        log.warn("Tipo de mídia não suportado: {}", ex.getContentType());

        String message = String.format("Tipo de mídia '%s' não é suportado", ex.getContentType());
        String details = String.format("Tipos de mídia suportados: %s", ex.getSupportedMediaTypes());

        ErrorResponse errorResponse = ErrorResponse.builder()
                .timestamp(LocalDateTime.now())
                .status(HttpStatus.UNSUPPORTED_MEDIA_TYPE.value())
                .error(HttpStatus.UNSUPPORTED_MEDIA_TYPE.getReasonPhrase())
                .message(message)
                .details(details)
                .path(getPath(request))
                .build();

        return ResponseEntity.status(HttpStatus.UNSUPPORTED_MEDIA_TYPE).body(errorResponse);
    }

    /**
     * Trata erros de regra de negócio
     */
    @ExceptionHandler(BusinessException.class)
    public ResponseEntity<ErrorResponse> handleBusinessException(
            BusinessException ex, WebRequest request) {

        log.warn("Erro de regra de negócio: {}", ex.getMessage());

        ErrorResponse errorResponse = ErrorResponse.builder()
                .timestamp(LocalDateTime.now())
                .status(HttpStatus.BAD_REQUEST.value())
                .error(HttpStatus.BAD_REQUEST.getReasonPhrase())
                .message(ex.getMessage())
                .details("Verifique os dados enviados e as regras de negócio")
                .path(getPath(request))
                .build();

        return ResponseEntity.badRequest().body(errorResponse);
    }

    /**
     * Trata erros relacionados a certificados digitais
     */
    @ExceptionHandler(CertificateException.class)
    public ResponseEntity<ErrorResponse> handleCertificateException(
            CertificateException ex, WebRequest request) {

        log.error("Erro de certificado: {}", ex.getMessage(), ex);

        ErrorResponse errorResponse = ErrorResponse.builder()
                .timestamp(LocalDateTime.now())
                .status(HttpStatus.BAD_REQUEST.value())
                .error(HttpStatus.BAD_REQUEST.getReasonPhrase())
                .message("Erro relacionado ao certificado digital")
                .details(ex.getMessage())
                .path(getPath(request))
                .build();

        return ResponseEntity.badRequest().body(errorResponse);
    }

    /**
     * Trata erros de assinatura digital de XML
     */
    @ExceptionHandler(XmlSigningException.class)
    public ResponseEntity<ErrorResponse> handleXmlSigningException(
            XmlSigningException ex, WebRequest request) {

        log.error("Erro de assinatura XML: {}", ex.getMessage(), ex);

        ErrorResponse errorResponse = ErrorResponse.builder()
                .timestamp(LocalDateTime.now())
                .status(HttpStatus.BAD_REQUEST.value())
                .error(HttpStatus.BAD_REQUEST.getReasonPhrase())
                .message("Erro na assinatura digital do XML")
                .details(ex.getMessage())
                .path(getPath(request))
                .build();

        return ResponseEntity.badRequest().body(errorResponse);
    }



    /**
     * Extrai o path da requisição
     */
    private String getPath(WebRequest request) {
        return request.getDescription(false).replace("uri=", "");
    }
}